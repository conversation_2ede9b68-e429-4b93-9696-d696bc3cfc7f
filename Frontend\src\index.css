@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here.
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 245 25% 12%;
    --foreground: 0 0% 98%;

    --card: 245 25% 12%;
    --card-foreground: 0 0% 98%;

    --popover: 245 25% 12%;
    --popover-foreground: 0 0% 98%;

    --primary: 278 70% 58%;
    --primary-foreground: 0 0% 98%;

    --secondary: 278 25% 25%;
    --secondary-foreground: 0 0% 98%;

    --muted: 278 25% 25%;
    --muted-foreground: 0 0% 85%;

    --accent: 278 70% 58%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 278 25% 25%;
    --input: 278 25% 25%;
    --ring: 278 70% 58%;

    --radius: 0.75rem;

    /* Modern gradients */
    --gradient-primary: linear-gradient(135deg, hsl(278 70% 58%) 0%, hsl(278 60% 50%) 100%);
    --gradient-secondary: linear-gradient(135deg, hsl(278 25% 25%) 0%, hsl(278 30% 35%) 100%);
    --gradient-bg: linear-gradient(135deg, hsl(245 25% 12%) 0%, hsl(278 25% 18%) 100%);

    /* Glass effects */
    --glass-bg: hsla(245, 25%, 12%, 0.95);
    --glass-border: hsla(278, 70%, 58%, 0.3);
    --glass-shadow: 0 8px 32px hsla(278, 70%, 58%, 0.4);

    /* Glow effects */
    --glow-primary: 0 0 20px hsla(278, 70%, 58%, 0.5);
    --glow-secondary: 0 0 40px hsla(278, 60%, 50%, 0.3);

    /* Transitions */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-spring: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }

  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }
}

@layer utilities {
  .backdrop-blur-glass {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
  }

  .transition-smooth {
    transition: var(--transition-smooth);
  }

  .transition-spring {
    transition: var(--transition-spring);
  }
}
