// API Configuration
const API_BASE_URL = 'http://localhost:8765/api';

// Types
export interface User {
  id: number;
  name: string;
  username: string;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  name: string;
  username: string;
  password: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  user?: User;
  users?: User[];
  errors?: Record<string, string>;
}

// API Error class
export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public response?: ApiResponse
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// Base API class
class ApiService {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const defaultOptions: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      credentials: 'include', // Include cookies for session management
    };

    const config = {
      ...defaultOptions,
      ...options,
      headers: {
        ...defaultOptions.headers,
        ...options.headers,
      },
    };

    try {
      const response = await fetch(url, config);
      
      // Handle non-JSON responses
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        throw new ApiError(
          'Server returned non-JSON response',
          response.status
        );
      }

      const data: ApiResponse<T> = await response.json();

      if (!response.ok) {
        throw new ApiError(
          data.message || `HTTP error! status: ${response.status}`,
          response.status,
          data
        );
      }

      return data;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      
      // Network or other errors
      throw new ApiError(
        error instanceof Error ? error.message : 'Network error occurred',
        0
      );
    }
  }

  // Authentication methods
  async login(credentials: LoginRequest): Promise<User> {
    const response = await this.request<User>('/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });

    if (!response.success || !response.user) {
      throw new ApiError(response.message || 'Login failed', 401, response);
    }

    return response.user;
  }

  async register(userData: RegisterRequest): Promise<User> {
    const response = await this.request<User>('/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });

    if (!response.success || !response.user) {
      throw new ApiError(
        response.message || 'Registration failed',
        400,
        response
      );
    }

    return response.user;
  }

  async logout(): Promise<void> {
    const response = await this.request('/logout', {
      method: 'POST',
    });

    if (!response.success) {
      throw new ApiError(response.message || 'Logout failed', 400, response);
    }
  }

  async getCurrentUser(): Promise<User | null> {
    try {
      const response = await this.request<User>('/me');
      
      if (response.success && response.user) {
        return response.user;
      }
      
      return null;
    } catch (error) {
      if (error instanceof ApiError && error.status === 401) {
        return null; // Not authenticated
      }
      throw error;
    }
  }

  // User management methods
  async getUsers(): Promise<User[]> {
    const response = await this.request<User[]>('/users');

    if (!response.success || !response.users) {
      throw new ApiError(
        response.message || 'Failed to fetch users',
        400,
        response
      );
    }

    return response.users;
  }

  async getUser(id: number): Promise<User> {
    const response = await this.request<User>(`/users/${id}`);

    if (!response.success || !response.user) {
      throw new ApiError(
        response.message || 'Failed to fetch user',
        400,
        response
      );
    }

    return response.user;
  }
}

// Create and export a singleton instance
export const api = new ApiService();

// Export the class for testing or custom instances
export default ApiService;
