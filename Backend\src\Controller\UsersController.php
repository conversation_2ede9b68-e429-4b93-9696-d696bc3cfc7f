<?php
declare(strict_types=1);

namespace App\Controller;

/**
 * Users Controller
 *
 * @property \App\Model\Table\UsersTable $Users
 */
class UsersController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */
    public function index()
    {
        $query = $this->Users->find();
        $users = $this->paginate($query);

        $this->set(compact('users'));
    }

    /**
     * View method
     *
     * @param string|null $id User id.
     * @return \Cake\Http\Response|null|void Renders view
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function view($id = null)
    {
        $user = $this->Users->get($id, contain: []);
        $this->set(compact('user'));
    }

    /**
     * Add method
     *
     * @return \Cake\Http\Response|null|void Redirects on successful add, renders view otherwise.
     */
    public function add()
    {
        $user = $this->Users->newEmptyEntity();
        if ($this->request->is('post')) {
            $user = $this->Users->patchEntity($user, $this->request->getData());
            if ($this->Users->save($user)) {
                $this->Flash->success(__('The user has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The user could not be saved. Please, try again.'));
        }
        $this->set(compact('user'));
    }

    /**
     * Register method
     * Permet à un nouvel utilisateur de s'inscrire
     */
    public function register()
    {
        $user = $this->Users->newEmptyEntity();
        if ($this->request->is('post')) {
            $data = $this->request->getData();
            // Hash du mot de passe
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
            $user = $this->Users->patchEntity($user, $data);
            if ($this->Users->save($user)) {
                $this->Flash->success(__('Inscription réussie. Vous pouvez maintenant vous connecter.'));
                return $this->redirect(['action' => 'login']);
            }
            $this->Flash->error(__('Impossible de s\'inscrire. Veuillez réessayer.'));
        }
        $this->set(compact('user'));
    }

    /**
     * Login method
     * Authentifie un utilisateur
     */
    public function login()
    {
        if ($this->request->is('post')) {
            $username = $this->request->getData('username');
            $password = $this->request->getData('password');
            $user = $this->Users->find()->where(['username' => $username])->first();
            if ($user && password_verify($password, $user->password)) {
                $this->request->getSession()->write('Auth.User', $user);
                $this->Flash->success(__('Connexion réussie.'));
                return $this->redirect(['controller' => 'Users', 'action' => 'index']);
            } else {
                $this->Flash->error(__('Nom d\'utilisateur ou mot de passe incorrect.'));
            }
        }
    }

    /**
     * Logout method
     * Déconnecte l'utilisateur
     */
    public function logout()
    {
        $this->request->getSession()->destroy();
        $this->Flash->success(__('Déconnexion réussie.'));
        return $this->redirect(['action' => 'login']);
    }

    /**
     * Edit method
     *
     * @param string|null $id User id.
     * @return \Cake\Http\Response|null|void Redirects on successful edit, renders view otherwise.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function edit($id = null)
    {
        $user = $this->Users->get($id, contain: []);
        if ($this->request->is(['patch', 'post', 'put'])) {
            $user = $this->Users->patchEntity($user, $this->request->getData());
            if ($this->Users->save($user)) {
                $this->Flash->success(__('The user has been saved.'));

                return $this->redirect(['action' => 'index']);
            }
            $this->Flash->error(__('The user could not be saved. Please, try again.'));
        }
        $this->set(compact('user'));
    }

    /**
     * Delete method
     *
     * @param string|null $id User id.
     * @return \Cake\Http\Response|null Redirects to index.
     * @throws \Cake\Datasource\Exception\RecordNotFoundException When record not found.
     */
    public function delete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $user = $this->Users->get($id);
        if ($this->Users->delete($user)) {
            $this->Flash->success(__('The user has been deleted.'));
        } else {
            $this->Flash->error(__('The user could not be deleted. Please, try again.'));
        }

        return $this->redirect(['action' => 'index']);
    }

    /**
     * API Login method
     * Authentifie un utilisateur via API
     */
    public function apiLogin()
    {
        $this->request->allowMethod(['post']);
        $this->viewBuilder()->setClassName('Json');

        if ($this->request->is('post')) {
            $username = $this->request->getData('username');
            $password = $this->request->getData('password');

            if (!$username || !$password) {
                $this->set([
                    'success' => false,
                    'message' => 'Username and password are required',
                    '_serialize' => ['success', 'message']
                ]);
                $this->response = $this->response->withStatus(400);
                return;
            }

            $user = $this->Users->find()->where(['username' => $username])->first();

            if ($user && password_verify($password, $user->password)) {
                // Store user in session for API
                $this->request->getSession()->write('Auth.User', $user);

                $this->set([
                    'success' => true,
                    'message' => 'Login successful',
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'username' => $user->username
                    ],
                    '_serialize' => ['success', 'message', 'user']
                ]);
            } else {
                $this->set([
                    'success' => false,
                    'message' => 'Invalid username or password',
                    '_serialize' => ['success', 'message']
                ]);
                $this->response = $this->response->withStatus(401);
            }
        }
    }

    /**
     * API Register method
     * Permet à un nouvel utilisateur de s'inscrire via API
     */
    public function apiRegister()
    {
        $this->request->allowMethod(['post']);
        $this->viewBuilder()->setClassName('Json');

        $user = $this->Users->newEmptyEntity();

        if ($this->request->is('post')) {
            $data = $this->request->getData();

            // Validation
            if (!isset($data['name']) || !isset($data['username']) || !isset($data['password'])) {
                $this->set([
                    'success' => false,
                    'message' => 'Name, username and password are required',
                    '_serialize' => ['success', 'message']
                ]);
                $this->response = $this->response->withStatus(400);
                return;
            }

            // Hash du mot de passe
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
            $user = $this->Users->patchEntity($user, $data);

            if ($this->Users->save($user)) {
                $this->set([
                    'success' => true,
                    'message' => 'Registration successful',
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'username' => $user->username
                    ],
                    '_serialize' => ['success', 'message', 'user']
                ]);
            } else {
                $errors = [];
                foreach ($user->getErrors() as $field => $error) {
                    $errors[$field] = is_array($error) ? implode(', ', $error) : $error;
                }

                $this->set([
                    'success' => false,
                    'message' => 'Registration failed',
                    'errors' => $errors,
                    '_serialize' => ['success', 'message', 'errors']
                ]);
                $this->response = $this->response->withStatus(400);
            }
        }
    }

    /**
     * API Logout method
     * Déconnecte l'utilisateur via API
     */
    public function apiLogout()
    {
        $this->request->allowMethod(['post']);
        $this->viewBuilder()->setClassName('Json');

        $this->request->getSession()->destroy();

        $this->set([
            'success' => true,
            'message' => 'Logout successful',
            '_serialize' => ['success', 'message']
        ]);
    }

    /**
     * API Me method
     * Retourne les informations de l'utilisateur connecté
     */
    public function apiMe()
    {
        $this->request->allowMethod(['get']);
        $this->viewBuilder()->setClassName('Json');

        $user = $this->request->getSession()->read('Auth.User');

        if ($user) {
            $this->set([
                'success' => true,
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'username' => $user->username
                ],
                '_serialize' => ['success', 'user']
            ]);
        } else {
            $this->set([
                'success' => false,
                'message' => 'Not authenticated',
                '_serialize' => ['success', 'message']
            ]);
            $this->response = $this->response->withStatus(401);
        }
    }

    /**
     * API Index method
     * Retourne la liste des utilisateurs via API
     */
    public function apiIndex()
    {
        $this->request->allowMethod(['get']);
        $this->viewBuilder()->setClassName('Json');

        // Check if user is authenticated
        $currentUser = $this->request->getSession()->read('Auth.User');
        if (!$currentUser) {
            $this->set([
                'success' => false,
                'message' => 'Authentication required',
                '_serialize' => ['success', 'message']
            ]);
            $this->response = $this->response->withStatus(401);
            return;
        }

        $query = $this->Users->find();
        $users = $this->paginate($query);

        $userList = [];
        foreach ($users as $user) {
            $userList[] = [
                'id' => $user->id,
                'name' => $user->name,
                'username' => $user->username
            ];
        }

        $this->set([
            'success' => true,
            'users' => $userList,
            '_serialize' => ['success', 'users']
        ]);
    }

    /**
     * API View method
     * Retourne un utilisateur spécifique via API
     */
    public function apiView($id = null)
    {
        $this->request->allowMethod(['get']);
        $this->viewBuilder()->setClassName('Json');

        // Check if user is authenticated
        $currentUser = $this->request->getSession()->read('Auth.User');
        if (!$currentUser) {
            $this->set([
                'success' => false,
                'message' => 'Authentication required',
                '_serialize' => ['success', 'message']
            ]);
            $this->response = $this->response->withStatus(401);
            return;
        }

        try {
            $user = $this->Users->get($id);
            $this->set([
                'success' => true,
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'username' => $user->username
                ],
                '_serialize' => ['success', 'user']
            ]);
        } catch (\Cake\Datasource\Exception\RecordNotFoundException $e) {
            $this->set([
                'success' => false,
                'message' => 'User not found',
                '_serialize' => ['success', 'message']
            ]);
            $this->response = $this->response->withStatus(404);
        }
    }
}
