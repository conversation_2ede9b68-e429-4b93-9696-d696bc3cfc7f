import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { api, User, ApiError } from '@/services/api';
import { useToast } from '@/hooks/use-toast';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (username: string, password: string) => Promise<void>;
  register: (name: string, username: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  const isAuthenticated = !!user;

  // Check if user is already authenticated on app start
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      setIsLoading(true);
      const currentUser = await api.getCurrentUser();
      setUser(currentUser);
    } catch (error) {
      console.error('Auth check failed:', error);
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (username: string, password: string) => {
    try {
      setIsLoading(true);
      const loggedInUser = await api.login({ username, password });
      setUser(loggedInUser);
      
      toast({
        title: "Connexion réussie !",
        description: `Bienvenue, ${loggedInUser.name}`,
      });
    } catch (error) {
      console.error('Login failed:', error);
      
      let errorMessage = 'Une erreur est survenue lors de la connexion';
      
      if (error instanceof ApiError) {
        if (error.status === 401) {
          errorMessage = 'Nom d\'utilisateur ou mot de passe incorrect';
        } else if (error.response?.message) {
          errorMessage = error.response.message;
        }
      }
      
      toast({
        title: "Erreur de connexion",
        description: errorMessage,
        variant: "destructive",
      });
      
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (name: string, username: string, password: string) => {
    try {
      setIsLoading(true);
      const newUser = await api.register({ name, username, password });
      setUser(newUser);
      
      toast({
        title: "Inscription réussie !",
        description: `Bienvenue, ${newUser.name}`,
      });
    } catch (error) {
      console.error('Registration failed:', error);
      
      let errorMessage = 'Une erreur est survenue lors de l\'inscription';
      
      if (error instanceof ApiError) {
        if (error.response?.errors) {
          // Handle validation errors
          const errors = Object.values(error.response.errors);
          errorMessage = errors.join(', ');
        } else if (error.response?.message) {
          errorMessage = error.response.message;
        }
      }
      
      toast({
        title: "Erreur d'inscription",
        description: errorMessage,
        variant: "destructive",
      });
      
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      setIsLoading(true);
      await api.logout();
      setUser(null);
      
      toast({
        title: "Déconnexion réussie",
        description: "À bientôt !",
      });
    } catch (error) {
      console.error('Logout failed:', error);
      // Even if logout fails on server, clear local state
      setUser(null);
      
      toast({
        title: "Déconnexion",
        description: "Vous avez été déconnecté",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const refreshUser = async () => {
    try {
      const currentUser = await api.getCurrentUser();
      setUser(currentUser);
    } catch (error) {
      console.error('Failed to refresh user:', error);
      setUser(null);
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    login,
    register,
    logout,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
